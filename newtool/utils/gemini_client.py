import os
import google.generativeai as genai
from utils.logger import get_logger

logger = get_logger(__name__)

class GeminiClient:
    def __init__(self, api_key: str = None):
        if api_key:
            genai.configure(api_key=api_key)
        else:
            # If no API key is provided, you can configure it through environment variables
            # or other means as per your setup.
            pass
        
        self.model = genai.GenerativeModel(
            'gemini-1.5-flash',
            tools=['grounding']
        )

    def make_request(self, prompt: str) -> str:
        """
        Makes a request to the Gemini API with the given prompt.

        Args:
            prompt: The prompt to send to the language model.

        Returns:
            The response from the language model as a string.
        """
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"An error occurred while making a request to the Gemini API: {e}", exc_info=True)
            return None