import os
import google.generativeai as genai
from utils.logger import get_logger

logger = get_logger(__name__)

class GeminiClient:
    def __init__(self, api_key: str = None):
        if api_key:
            genai.configure(api_key=api_key)
        else:
            # If no API key is provided, configure it through environment variables
            api_key = os.getenv('GEMINI_API_KEY')
            if api_key:
                genai.configure(api_key=api_key)
            else:
                logger.warning("No Gemini API key provided. Please set GEMINI_API_KEY environment variable.")

        # Updated to use Gemini 2.5 Flash with URL context tool and Google search grounding
        self.model = genai.GenerativeModel(
            'gemini-2.5-flash',
            tools=['grounding', 'url_context']
        )

    def make_request(self, prompt: str, url: str = None) -> str:
        """
        Makes a request to the Gemini API with the given prompt.

        Args:
            prompt: The prompt to send to the language model.
            url: Optional URL for context when analyzing specific websites.

        Returns:
            The response from the language model as a string.
        """
        try:
            logger.info(f"Making request to Gemini 2.5 Flash API...")
            if url:
                logger.info(f"Including URL context: {url}")

            # Generate content with the updated model
            response = self.model.generate_content(prompt)

            if response and response.text:
                logger.info(f"Received response from Gemini API (length: {len(response.text)} characters)")
                logger.debug(f"Full response: {response.text}")
                return response.text
            else:
                logger.warning("Received empty response from Gemini API")
                return None

        except Exception as e:
            logger.error(f"An error occurred while making a request to the Gemini API: {e}", exc_info=True)
            return None