from fastapi import FastAPI, HTTPException
from api.request import WebsiteHealthAnalysisRequest
from utils.url_processor import process_urls
from services.reachability_service import check_url_reachability
from services.health_analysis_service import analyze_website_health
from utils.logger import get_logger

app = FastAPI()
logger = get_logger(__name__)

@app.post("/analyze-website-health")
async def analyze_health(request: WebsiteHealthAnalysisRequest):
    """
    Analyzes the health of a website by processing its URLs, checking for
    reachability, and performing a health analysis.
    """
    logger.info(f"Received request for website: {request.website}")
    try:
        # 1. Process URLs
        logger.info("Processing URLs...")
        url_dict = process_urls(request.parsed_urls)
        logger.info(f"Processed {len(url_dict)} unique URLs.")

        # 2. Check URL reachability
        logger.info("Checking URL reachability...")
        reachable_urls = check_url_reachability(url_dict)
        logger.info(f"Found {len(reachable_urls)} reachable URLs.")

        # 3. Analyze website health
        logger.info("Analyzing website health...")
        analysis_results = analyze_website_health(reachable_urls, request.website)
        logger.info("Website health analysis complete.")

        return {"status": "success", "results": analysis_results}
    except Exception as e:
        logger.error(f"An error occurred: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
    
@app.get("/health-check")
async def health_check():
    """
    Health check endpoint to verify API is running.
    """
    return {"status": "api is running", "version": "1.0.0"}