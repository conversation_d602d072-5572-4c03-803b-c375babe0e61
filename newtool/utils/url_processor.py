from typing import List, Dict
from api.request import UrlDepthItem

def process_urls(parsed_urls: List[UrlDepthItem]) -> Dict[int, str]:
    """
    Processes a list of URLs by deduplicating, sorting, and selecting the top 100.

    Args:
        parsed_urls: A list of UrlDepthItem objects from the request.

    Returns:
        A dictionary of the top 100 URLs, with integer keys.
    """
    # 1. Get all extracted URLs
    all_urls = [url for item in parsed_urls for url in item.urls]

    # 2. Dedupe all the URLs
    unique_urls = sorted(list(set(all_urls)))

    # 3. Sort the URLs in ascending order of length and take top 100
    sorted_urls = sorted(unique_urls, key=len)[:100]

    # 4. Convert it into a dictionary
    url_dict = {i: url for i, url in enumerate(sorted_urls)}

    return url_dict