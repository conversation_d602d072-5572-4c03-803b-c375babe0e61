import json
from typing import List
from utils.prompts import GptPromptPicker
from utils.logger import get_logger
from utils.gemini_client import GeminiClient

logger = get_logger(__name__)
gemini_client = GeminiClient()

def analyze_website_health(reachable_urls: List[str], home_page_url: str) -> List[dict]:
    """
    Analyzes the health of a list of URLs and returns the analysis results.

    Args:
        reachable_urls: A list of reachable URLs.
        home_page_url: The URL of the home page.

    Returns:
        A list of dictionaries, where each dictionary contains the health analysis
        results for a single URL.
    """
    # 5. Dedupe the final list of reachable URL + home page URL
    final_urls = sorted(list(set(reachable_urls + [home_page_url])))

    analysis_results = []

    # 6. Pass just once 1 URLs at one time
    for url in final_urls:
        prompt = GptPromptPicker.get_website_health_analysis_prompt(website_url=url)
        response_text = gemini_client.make_request(prompt)

        if response_text:
            try:
                response_json = json.loads(response_text)
                logger.info(f"Health analysis for {url}: {response_json}")
                analysis_results.append(response_json)
            except json.JSONDecodeError:
                logger.error(f"Failed to decode JSON from Gemini API response for URL: {url}")

    return analysis_results